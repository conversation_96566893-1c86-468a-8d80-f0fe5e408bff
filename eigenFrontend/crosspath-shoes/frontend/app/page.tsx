"use client";

import Link from "next/link";
import Image from "next/image";
import { TypeAnimation } from "react-type-animation";
import { motion } from "framer-motion";
import { LandingHeader } from "@/components/landing-header";
import RunningCards from "@/components/landing/RunningCards";

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" }, // Slightly faster duration
  },
};

const staggerVariant = {
  hidden: { opacity: 0 }, // Add hidden state for stagger parent
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1, // Faster stagger
      delayChildren: 0.1, // Small delay before children start
    },
  },
};

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen font-mono overflow-x-hidden bg-white text-foreground relative">
      {/* Use the LandingHeader component */}
      <LandingHeader />

      {/* Main content needs z-index to be above background */}
      <main className="flex-1 z-10">
        <motion.section
          className="relative flex items-center justify-center w-full py-20 sm:py-28 min-h-[75vh] overflow-hidden"
          initial="hidden"
          animate="visible"
          variants={staggerVariant}
        >
          {/* Hero video background - no overlays for clean color inversion */}
          <div className="absolute inset-0 z-0 w-full h-full overflow-hidden">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute h-full w-full object-cover object-center"
            >
              <source src="/videos/runner_video.mp4" type="video/mp4" />
            </video>
          </div>

          {/* Container for text positioned at the bottom and centered */}
          <div className="w-full z-10 flex flex-col items-center justify-end h-full absolute inset-x-0 bottom-0 pb-12 sm:pb-16">
            <div className="text-center w-full max-w-3xl mx-auto px-4 relative">
              {/* Text with black color */}
              <div className="relative z-20 text-center">
                <motion.h1
                  className="text-4xl sm:text-5xl md:text-6xl font-bold text-black leading-tight text-center flex flex-col items-center gap-0"
                  variants={fadeInVariant}
                >
                  <div className="flex justify-center">
                    <TypeAnimation
                      sequence={[
                        "Eigen",
                        2000,
                        "Precision",
                        1500,
                        "Optimized",
                        1500,
                        "Firmware",
                        1500,
                      ]}
                      wrapper="span"
                      speed={10}
                      deletionSpeed={60}
                      className="inline-block font-sans font-medium"
                      repeat={Infinity}
                    />
                  </div>
                  <span className="font-sans font-extrabold text-4xl sm:text-5xl md:text-6xl relative -mt-1">
                    for your stride
                  </span>
                </motion.h1>
              </div>

              {/* Compile Your Profile Button */}
              <motion.div
                className="mt-3 sm:mt-4 flex justify-center relative z-10"
                variants={fadeInVariant}
              >
                <Link href="/signup" passHref>
                  <motion.span
                    className="group relative inline-flex items-center px-6 py-3 bg-primary text-primary-foreground text-sm sm:text-base font-sans font-medium rounded-md cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300"
                    whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span className="flex items-center">
                      Compile Your Profile
                      <span className="ml-1">&gt;</span>
                    </span>
                  </motion.span>
                </Link>
              </motion.div>
            </div>
          </div>
        </motion.section>

        {/* Feature Cards Section */}
        <motion.section
          className="relative py-12 sm:py-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-7xl mx-auto z-10 relative px-4">
            <RunningCards />
          </div>
        </motion.section>

        {/* What is eigen Section - Short Summary */}
        <motion.section
          className="relative py-10 sm:py-12 bg-white border-t border-b border-border/20"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-3xl mx-auto px-4 sm:px-6 z-10 relative">
            <motion.div
              variants={fadeInVariant}
              className="p-5 bg-white border border-border/30 rounded-lg shadow-md relative z-10 overflow-hidden"
            >
              <div className="relative">
                <h2 className="font-sans text-3xl sm:text-4xl font-medium text-foreground mb-4 flex items-center">
                  <span className="text-primary/80 mr-2">//</span> What is{" "}
                  <span className="text-primary ml-2 relative">
                    eigen
                    <motion.span
                      className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary/50"
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      viewport={{ once: true }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    ></motion.span>
                  </span>
                  ?
                </h2>
                <div className="space-y-3 font-jetbrains text-sm sm:text-base text-muted-foreground p-2">
                  <p>
                    &gt; A biomechanical analysis platform that matches your
                    unique running characteristics to the perfect shoes.
                  </p>
                  <p>
                    &gt; We analyze foot structure, gait patterns, and running
                    preferences to create your personalized profile.
                  </p>
                  <p>
                    &gt; Our algorithms identify the precise shoe
                    characteristics that will optimize your performance and
                    reduce injury risk.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.section>
        {/* Founders Section */}
        <motion.section
          className="relative py-10 sm:py-12 bg-secondary"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.1 }}
          variants={staggerVariant}
        >
          <div className="max-w-5xl mx-auto px-4 sm:px-6 z-10 relative">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <motion.div
                variants={fadeInVariant}
                className="order-2 md:order-1"
              >
                <div className="bg-white border border-border/30 rounded-lg shadow-md overflow-hidden relative">
                  <div className="relative">
                    <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-border/30">
                      <div className="flex space-x-1.5">
                        <span className="w-2.5 h-2.5 bg-red-400 rounded-full"></span>
                        <span className="w-2.5 h-2.5 bg-yellow-400 rounded-full"></span>
                        <span className="w-2.5 h-2.5 bg-green-400 rounded-full"></span>
                      </div>
                      <p className="text-xs text-gray-500 font-mono">
                        eigen_founders.js
                      </p>
                    </div>

                    <div className="p-4 text-sm text-left overflow-x-auto font-input relative">
                      <pre className="whitespace-pre-wrap relative z-10">
                        <code className="language-javascript text-gray-800 block">{`// About Us

// founders
Till Findl (UCL Medicine)
Max Krause (ETH Zurich Robotics)

// problem
Met on a run in London and ran into the same problem:
finding the right running shoe felt like guesswork.
Too many options. Too much branding. No link to how
you actually move.

// solution
We built eigenFIT: personalised shoe recommendations
based on your stride. Like a running store and lab
analysis in your pocket.`}</code>
                      </pre>

                      {/* Simple cursor animation */}
                      <motion.div
                        className="absolute bottom-4 left-[calc(8rem)] w-1.5 h-4 bg-primary/70"
                        animate={{ opacity: [1, 0, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      ></motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                variants={fadeInVariant}
                className="order-1 md:order-2"
              >
                <Image
                  src="/images/eigen_founders.jpg"
                  alt="Eigen Founders"
                  width={500}
                  height={400}
                  className="rounded-lg shadow-md object-cover w-full"
                />
              </motion.div>
            </div>
          </div>
        </motion.section>
        {/* CTA Section - Simplified */}
        <motion.section
          className="relative w-full py-10 sm:py-12 overflow-hidden"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          variants={staggerVariant}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 text-center z-10 relative">
            <motion.div
              variants={fadeInVariant}
              className="p-6 sm:p-8 md:p-10 bg-white rounded-lg border border-border/30 shadow-md relative overflow-hidden"
            >
              <div className="relative">
                <motion.h2
                  className="text-4xl sm:text-5xl md:text-6xl font-sans font-semibold tracking-tight text-foreground max-w-3xl mx-auto"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  Ready to{" "}
                  <span className="text-primary relative inline-block">
                    compile
                    <motion.span
                      className="absolute -bottom-1 left-0 w-full h-1 bg-primary/50"
                      initial={{ width: 0 }}
                      whileInView={{ width: "100%" }}
                      viewport={{ once: true }}
                      transition={{ delay: 0.5, duration: 1, ease: "easeOut" }}
                    ></motion.span>
                  </span>{" "}
                  your <br className="sm:hidden" />
                  perfect shoe match?
                </motion.h2>

                <motion.p
                  className="mt-4 sm:mt-5 md:mt-6 text-base sm:text-lg text-foreground/90 font-jetbrains max-w-xl mx-auto leading-relaxed p-3"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  &gt; ~5 minutes input for personalized, data-driven
                  recommendations.
                </motion.p>

                <motion.div
                  className="mt-6 sm:mt-8"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5, duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <Link href="/signup" passHref>
                    <motion.span
                      className="group relative inline-flex items-center px-8 py-4 bg-primary text-primary-foreground text-base sm:text-lg font-sans font-medium rounded-md cursor-pointer shadow-md transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="relative z-10 flex items-center">
                        Start Free Analysis
                        <span className="ml-1">&gt;</span>
                      </span>
                    </motion.span>
                  </Link>
                </motion.div>

                <motion.p
                  className="mt-6 sm:mt-8 text-xs sm:text-sm text-foreground/60 font-input italic"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ delay: 0.7, duration: 0.8 }}
                  viewport={{ once: true }}
                >
                  <span className="text-primary/80 mr-2">//</span> Built by
                  engineers & biomechanists. Not marketers.
                </motion.p>
              </div>
            </motion.div>
          </div>
        </motion.section>
      </main>

      {/* Footer with simplified design */}
      <footer className="relative p-5 sm:p-6 bg-white border-t border-border/30 z-10">
        <div className="max-w-7xl mx-auto text-center font-light text-xs sm:text-sm text-muted-foreground space-y-2 px-4 sm:px-6">
          <p className="font-mono">
            <span className="text-primary/80 mr-1">//</span> &copy;{" "}
            {new Date().getFullYear()} CrossPath Labs{" "}
            <span className="text-primary/80">eigen</span> Division
          </p>

          <p className="max-w-xl mx-auto font-jetbrains">
            Transparency: Equal, standardized affiliate payment
            post-recommendation. Your data drives results, not brand deals.
          </p>

          <div className="pt-3 space-x-4 flex justify-center items-center">
            <Link
              href="/privacy"
              className="relative hover:text-primary font-input"
            >
              Privacy Policy
            </Link>
            <span className="text-primary/30">|</span>
            <Link
              href="/terms"
              className="relative hover:text-primary font-input"
            >
              Terms of Service
            </Link>
            <span className="text-primary/30">|</span>
            <Link href="#" className="relative hover:text-primary font-input">
              Contact
            </Link>
          </div>

          <div className="pt-2 text-[10px] text-muted-foreground/60 font-input">
            <span className="text-primary/50 mr-1">//</span> Built with
            precision for runners, by runners
          </div>
        </div>
      </footer>
    </div>
  );
}
