"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { VideoUploadStep } from '@/components/VideoUploadStep'; // Import the new component

export default function RunningVideoSagittalPage() {
  const { steps } = useRunningProfile();
  // Find index based on this page's path
  const currentStepIndex = steps.findIndex(step => step.href.includes('running-videos/sagittal'));
  const nextStep = steps[currentStepIndex + 1]; // Should be wearable-data step
  const prevStep = steps[currentStepIndex - 1];

  return (
    <VideoUploadStep
      stepTitle="Running Video: Sagittal" // Updated title
      stepDescription="Upload a short (10-15 seconds) video of you running, recorded directly from the side."
      videoUrlFieldName={"runningVideoSagittalUrl"} // Correct field name for Prisma model
      videoType="sagittal" // Pass video type for guidance
      nextStepHref={nextStep?.href || '/'} 
      backStepHref={prevStep?.href || '/'} 
      currentStepIndex={currentStepIndex}
    />
  );
} 