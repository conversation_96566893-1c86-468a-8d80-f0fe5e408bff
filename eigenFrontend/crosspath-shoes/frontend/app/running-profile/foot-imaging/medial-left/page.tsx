"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { ImageUploadStep } from '@/components/image-upload-step';

export default function FootImageMedialLeftPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/medial-left'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <ImageUploadStep
      stepTitle="Foot Image: Medial Left"
      stepDescription="Upload a clear photo of the inner (medial) side of your LEFT foot."
      imageUrlFieldName="footImageMedialLeftUrl"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
} 