"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { ImageUploadStep } from '@/components/image-upload-step';

export default function FootImageTopLeftPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/top-left'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <ImageUploadStep
      stepTitle="Foot Image: Top Left"
      stepDescription="Upload a clear top-down photo of your LEFT foot placed on a standard A4 paper against a wall."
      imageUrlFieldName={"footImageTopLeftUrl"}
      nextStepHref={nextStep?.href || '/'} 
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
} 