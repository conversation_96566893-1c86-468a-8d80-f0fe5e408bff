"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { ImageUploadStep } from '@/components/image-upload-step';

export default function FootImageTopRightPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/top-right'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <ImageUploadStep
      stepTitle="Foot Image: Top Right"
      stepDescription="Upload a clear top-down photo of your RIGHT foot placed on a standard A4 paper against a wall."
      imageUrlFieldName="footImageTopRightUrl"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
} 