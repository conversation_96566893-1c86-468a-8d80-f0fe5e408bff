"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { ImageUploadStep } from '@/components/image-upload-step';

export default function FootImageMedialRightPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/medial-right'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <ImageUploadStep
      stepTitle="Foot Image: Medial Right"
      stepDescription="Upload a clear photo of the inner (medial) side of your RIGHT foot."
      imageUrlFieldName="footImageMedialRightUrl"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
} 