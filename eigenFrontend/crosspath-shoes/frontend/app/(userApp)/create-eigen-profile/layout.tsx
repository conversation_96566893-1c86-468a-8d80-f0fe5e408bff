import type React from "react";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/server-auth";
import { DashboardHeader } from "@/components/dashboard-header";
import { CreateProfileStepper } from "@/components/create-running-profile/CreateProfileStepper";
import { RunningProfileClientWrapper } from "./client-wrapper";

export default async function RunningProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get the current user with error handling
  let user;
  try {
    user = await getCurrentUser();

    // Redirect to sign in if not authenticated
    if (!user) {
      console.log("No authenticated user found, redirecting to signin");
      redirect("/signin");
    }
  } catch (error) {
    // Log the error and redirect to sign in
    console.error("Error in RunningProfileLayout:", error);
    redirect("/signin");
  }

  // If we get here, we have a valid user
  return (
    <div className="flex min-h-screen flex-col bg-zinc-50 dark:bg-brand-black">
      <DashboardHeader />
      <div className="flex-1 w-full px-4 md:px-6 md:container md:mx-auto py-4 md:py-6">
        <RunningProfileClientWrapper>
          <CreateProfileStepper />
          <main className="relative py-4 md:py-6 lg:py-8 text-brand-black dark:text-text-main">
            <div className="w-full">{children}</div>
          </main>
        </RunningProfileClientWrapper>
      </div>
    </div>
  );
}
