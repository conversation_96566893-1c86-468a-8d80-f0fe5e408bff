"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowRight, Footprints, Info } from "lucide-react";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { motion } from "framer-motion";
import { createOrUpdateRunningProfile } from "@/actions/running-profile";
import { toast } from "sonner";

export default function RunningProfileStartPage() {
  const router = useRouter();
  const { steps, setCurrentStep, setProfileId } = useRunningProfile();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleStart = async () => {
    setIsSubmitting(true);
    setError(null);

    const result = await createOrUpdateRunningProfile("New Running Profile");

    if (result.error || !result.profileId) {
      const errorMessage =
        result.error || "Failed to create profile. Please try again.";
      setError(errorMessage);
      toast.error(`Error: ${errorMessage}`);
      setIsSubmitting(false);
      return;
    }

    setProfileId(result.profileId);

    const firstStepIndex = steps.findIndex((step) =>
      step.href.includes("foot-imaging/top-left")
    );
    const firstStepHref = steps[firstStepIndex]?.href;

    if (firstStepHref) {
      if (firstStepIndex !== -1) {
        setCurrentStep(firstStepIndex);
      }
      toast.success("Let's start your profile!");
      router.push(firstStepHref);
    } else {
      toast.error(
        "Could not determine the first step. Please contact support."
      );
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-full md:max-w-3xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6 sm:space-y-8"
      >
        <div className="space-y-2 text-center">
          <div className="inline-flex items-center justify-center p-3 bg-emerald-100/60 dark:bg-sweetspot-black/60 border border-emerald-900/20 dark:border-text-main/20 rounded-full mb-2">
            <Footprints className="h-6 w-6 sm:h-8 sm:w-8 text-brand-green" />
          </div>
          <h1 className="font-sans text-3xl sm:text-4xl font-medium text-brand-black dark:text-text-main">
            Your Perfect Running Shoes Await
          </h1>
          <p className="font-jetbrains text-base text-brand-black/80 dark:text-text-main/80 max-w-full sm:max-w-2xl mx-auto pt-1">
            Create your running profile to get personalized shoe recommendations
            backed by AI analysis. We'll analyze your foot structure and running
            style to find your perfect match.
          </p>
        </div>

        <div className="grid gap-4 sm:gap-6">
          <Card className="border border-brand-black/15 dark:border-text-main/15 bg-zinc-100 dark:bg-terminal-black shadow-md">
            <CardHeader>
              <CardTitle className="font-sans text-2xl font-medium text-brand-black dark:text-text-main">
                Running Profile Creation Process
              </CardTitle>
              <CardDescription className="font-input text-sm text-brand-black/60 dark:text-text-main/60 pt-1">
                Follow these steps to complete your running profile:
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {[
                  {
                    title: "Foot Photos",
                    desc: "Upload top-down and medial photos of both feet.",
                  },
                  {
                    title: "Running Videos",
                    desc: "Upload short posterior (back) and sagittal (side) view videos.",
                  },
                  {
                    title: "Wearable Data",
                    desc: "Sync fitness apps or manually enter your running stats.",
                  },
                  {
                    title: "Runner Profile",
                    desc: "Provide details about yourself and any injury history.",
                  },
                  {
                    title: "Review & Save",
                    desc: "Confirm your details and get recommendations!",
                  },
                ].map((step, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <span className="flex h-6 w-6 items-center justify-center rounded-full bg-emerald-100/80 dark:bg-sweetspot-black/80 border border-emerald-900/20 dark:border-text-main/20 text-brand-green text-xs font-mono mt-0.5">
                      {index + 1}
                    </span>
                    <div className="flex-1">
                      <p className="font-sans font-medium text-brand-black/90 dark:text-text-main/90 text-base">
                        {step.title}
                      </p>
                      <p className="text-sm font-input text-brand-black/60 dark:text-text-main/60 leading-snug mt-0.5">
                        {step.desc}
                      </p>
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="flex justify-center"
          >
            <Button
              onClick={handleStart}
              className="w-full max-w-xs group relative inline-flex items-center justify-center px-6 py-3 bg-brand-black text-off-white dark:bg-off-white dark:text-text-inverted text-base font-sans font-medium rounded-sm cursor-pointer shadow-md hover:shadow-lg hover:bg-brand-black/90 dark:hover:bg-off-white/90 transition-all duration-300 overflow-hidden disabled:opacity-60 disabled:cursor-not-allowed"
              size="lg"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center gap-2">
                  <svg
                    className="animate-spin h-4 w-4 text-inherit"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Starting...
                </span>
              ) : (
                <span className="relative z-10 flex items-center justify-center gap-2">
                  Let's Begin <ArrowRight className="h-4 w-4 ml-1" />
                </span>
              )}
            </Button>
          </motion.div>

          {error && (
            <p className="text-center text-sm text-red-600 dark:text-red-400 mt-2 font-input">
              {error}
            </p>
          )}

          <Card className="bg-zinc-100/60 dark:bg-sweetspot-black/60 border border-brand-black/20 dark:border-text-main/20">
            <CardContent className="pt-6">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-brand-black/50 dark:text-text-main/50 mt-0.5 flex-shrink-0" />
                <p className="text-sm font-input text-brand-black/60 dark:text-text-main/60 leading-relaxed">
                  The more accurate your photos and information, the better our
                  AI can analyze your feet and running style to recommend the
                  perfect shoes for you.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </div>
  );
}
