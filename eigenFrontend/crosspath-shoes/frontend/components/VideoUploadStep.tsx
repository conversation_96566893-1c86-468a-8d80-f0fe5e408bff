import React, { useState, useEffect, useTransition } from "react";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  UploadCloud,
  Trash2,
  ArrowRight,
  ArrowLeft,
  Loader2,
  Video,
  Sparkles,
  AlertTriangle,
} from "lucide-react";
import { updateRunningProfileDetails } from "@/actions/running-profile";
import { uploadFile } from "@/lib/upload-helpers";
import { toast } from "sonner";
import { RunningVideoGuidance } from "./RunningVideoGuidance";
import { SkeletonLoader } from "./SkeletonLoader";
import { OpenPoseVideo } from "./openpose-video";
import { Al<PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

// Define a specific type for the VIDEO URL fields this component handles
type VideoUrlFieldName = "runningVideoPosteriorUrl" | "runningVideoSagittalUrl";

interface VideoUploadStepProps {
  stepTitle: string;
  stepDescription: string;
  videoUrlFieldName: VideoUrlFieldName; // Use the specific union type
  videoType: "posterior" | "sagittal"; // To pass to guidance
  nextStepHref: string;
  backStepHref: string;
  currentStepIndex: number;
}

export const VideoUploadStep: React.FC<VideoUploadStepProps> = ({
  stepTitle,
  stepDescription,
  videoUrlFieldName,
  videoType,
  nextStepHref,
  backStepHref,
  currentStepIndex,
}) => {
  const { profileId, profileData, updateProfileData, setCurrentStep } =
    useRunningProfile();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Load existing video URL from profileData on initial mount
  useEffect(() => {
    const existingUrl = profileData[videoUrlFieldName] as string | null;
    if (existingUrl) {
      setPreviewUrl(existingUrl);
    }
  }, [profileData, videoUrlFieldName]);

  // Update preview when a new file is selected
  useEffect(() => {
    if (videoFile) {
      const objectUrl = URL.createObjectURL(videoFile);
      setPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl); // Clean up object URL
    }
    // If file is removed, reset preview to the stored URL if it exists
    else if (profileData[videoUrlFieldName]) {
      setPreviewUrl(profileData[videoUrlFieldName] as string | null);
    } else {
      setPreviewUrl(null);
    }
  }, [videoFile, profileData, videoUrlFieldName]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      // Basic validation for video type
      if (!file.type.startsWith("video/")) {
        setError("Invalid file type. Please upload a video file.");
        toast.error("Invalid file type. Please upload a video file.");
        return;
      }
      setVideoFile(file);
      setError(null); // Clear previous errors
    }
  };

  const handleRemoveVideo = () => {
    setVideoFile(null);
    setError(null);
    setPreviewUrl(profileData[videoUrlFieldName] as string | null);
    // Add action to delete from storage/db if needed
  };

  const handleSubmit = () => {
    if (!videoFile) {
      setError("Please select a video file to upload.");
      return;
    }
    if (!profileId) {
      setError("Profile ID is missing. Cannot upload video.");
      toast.error("Error: Profile not found. Please go back and start again.");
      return;
    }

    setError(null);
    setIsUploading(true);

    startTransition(async () => {
      try {
        // Upload the file using our new helper function
        const result = await uploadFile(
          videoFile,
          profileId,
          videoUrlFieldName
        );

        if (result.error) {
          throw new Error(result.error);
        }

        if (result.fileUrl) {
          // Update the profile data in the context
          updateProfileData({ [videoUrlFieldName]: result.fileUrl });
          toast.success("Video uploaded successfully!");
          setCurrentStep(currentStepIndex + 1);
          router.push(nextStepHref);
        }
      } catch (err) {
        const message =
          err instanceof Error ? err.message : "An unexpected error occurred.";
        setError(`Upload failed: ${message}`);
        toast.error(`Upload failed: ${message}`);
      } finally {
        setIsUploading(false);
      }
    });
  };

  const handleSkipWithFakeData = () => {
    if (!profileId) {
      setError("Profile ID is missing. Cannot continue.");
      toast.error("Error: Profile not found. Please go back and start again.");
      return;
    }

    setError(null);
    setIsUploading(true);

    startTransition(async () => {
      try {
        // Update the profile with a flag indicating this field should use fake data
        const result = await updateRunningProfileDetails(profileId, {
          [videoUrlFieldName]: "fake-data-placeholder",
        });

        if (result.error) {
          throw new Error(result.error);
        }

        toast.success("Using AI-generated data for this step!");
        setCurrentStep(currentStepIndex + 1);
        router.push(nextStepHref);
      } catch (err) {
        const message =
          err instanceof Error ? err.message : "An unexpected error occurred.";
        setError(`Failed to continue: ${message}`);
        toast.error(`Failed to continue: ${message}`);
      } finally {
        setIsUploading(false);
      }
    });
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(backStepHref);
  };

  const isLoading = isUploading || isPending;

  return (
    <div className="flex flex-col items-center justify-start min-h-[calc(100vh-150px)]">
      <Card className="w-full max-w-xl mx-auto shadow-lg bg-white dark:bg-terminal-black border border-brand-black/15 dark:border-text-main/15">
        <CardHeader>
          <CardTitle className="font-sans text-2xl sm:text-3xl font-medium text-brand-black dark:text-text-main">
            {stepTitle}
          </CardTitle>
          <CardDescription className="font-input text-sm sm:text-base text-brand-black/60 dark:text-text-main/60 pt-1">
            {stepDescription}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <RunningVideoGuidance viewType={videoType} />

          {isLoading && <SkeletonLoader className="my-4" />}

          {!isLoading && (
            <>
              {error && (
                <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-500/50 dark:border-red-500/50 text-sm rounded-sm font-input">
                  {error}
                </div>
              )}

              <Alert className="mb-4 bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800">
                <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-500" />
                <AlertTitle className="text-amber-800 dark:text-amber-500">
                  No video?
                </AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400 text-sm">
                  You can skip this step and our AI will generate data for you.
                  Just click the "Skip with AI Data" button below.
                </AlertDescription>
              </Alert>

              <div className="mt-4">
                <div className="flex flex-col items-stretch gap-2">
                  <Input
                    id="videoUpload"
                    type="file"
                    accept="video/*"
                    onChange={handleFileChange}
                    className="hidden"
                    disabled={isLoading}
                  />
                  <Label
                    htmlFor="videoUpload"
                    className={`flex items-center justify-center gap-2 cursor-pointer rounded-sm border border-brand-black/30 dark:border-text-main/30 bg-zinc-100/60 dark:bg-sweetspot-black/60 px-4 py-2 text-sm font-input text-brand-black/80 dark:text-text-main/80 shadow-sm transition-colors hover:bg-zinc-100/80 dark:hover:bg-sweetspot-black/80 ${
                      isLoading ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    <UploadCloud className="h-4 w-4" />
                    {videoFile ? "Change Video" : "Select Video"}
                  </Label>
                  {(videoFile || profileData[videoUrlFieldName]) && (
                    <Button
                      variant="outline"
                      onClick={handleRemoveVideo}
                      aria-label="Remove video"
                      disabled={isLoading}
                      className="flex items-center justify-center gap-2 text-red-600 hover:text-red-700 dark:text-red-500 dark:hover:text-red-400 border-red-500/50 hover:border-red-500/70 dark:border-red-500/60 dark:hover:border-red-500/80 hover:bg-red-500/5 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                      Remove Current Video
                    </Button>
                  )}
                </div>
              </div>

              <div className="mt-6 border border-brand-black/20 dark:border-text-main/20 rounded-sm p-2 bg-zinc-100/40 dark:bg-sweetspot-black/40 aspect-video min-h-[200px] w-full">
                {previewUrl ? (
                  <div className="w-full h-full">
                    {/* Original video preview */}
                    <video
                      src={previewUrl}
                      controls
                      className="max-w-full max-h-[300px] object-contain rounded-sm mx-auto"
                    >
                      Your browser does not support the video tag.
                    </video>

                    {/* OpenPose component will be shown after upload */}
                    {profileData[videoUrlFieldName] && profileId && (
                      <div className="mt-4">
                        <OpenPoseVideo
                          title="OpenPose Analysis"
                          description="Process this video with OpenPose to see skeleton overlay"
                          videoSrc={
                            profileData[videoUrlFieldName] as string | null
                          }
                          profileId={profileId}
                          videoType={videoType}
                        />
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex flex-col justify-center items-center text-center">
                    <Video className="h-10 w-10 text-brand-black/40 dark:text-text-main/40 mb-2" />
                    <p className="text-sm font-input text-brand-black/50 dark:text-text-main/50">
                      Select a video to upload.
                    </p>
                    <p className="text-xs font-input text-brand-black/40 dark:text-text-main/40 mt-1">
                      Preview will appear here.
                    </p>
                  </div>
                )}
              </div>
            </>
          )}
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 mt-4">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={isLoading}
            className="w-full sm:w-auto border-brand-black/30 dark:border-text-main/30 hover:bg-zinc-100/70 dark:hover:bg-sweetspot-black/70 hover:text-brand-black/90 dark:hover:text-text-main/90 text-brand-black/70 dark:text-text-main/70"
          >
            <ArrowLeft className="mr-2 h-4 w-4" /> Back
          </Button>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto sm:ml-auto">
            <Button
              variant="outline"
              onClick={handleSkipWithFakeData}
              disabled={isLoading}
              className="w-full sm:w-auto border-amber-300 dark:border-amber-700 bg-amber-50 dark:bg-amber-950/30 hover:bg-amber-100 dark:hover:bg-amber-900/30 text-amber-700 dark:text-amber-500 hover:text-amber-800 dark:hover:text-amber-400"
            >
              <Sparkles className="mr-2 h-4 w-4" /> Skip with AI Data
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!videoFile || isLoading}
              className="w-full sm:w-auto group relative inline-flex items-center justify-center px-6 py-2 bg-brand-black text-off-white dark:bg-off-white dark:text-text-inverted text-sm font-sans font-medium rounded-sm cursor-pointer shadow-md hover:shadow-lg hover:bg-brand-black/90 dark:hover:bg-off-white/90 transition-colors duration-300 overflow-hidden disabled:opacity-60 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin text-inherit" />
                  {videoFile ? "Uploading..." : "Processing..."}
                </span>
              ) : (
                <span className="relative z-10 flex items-center justify-center">
                  Upload & Continue <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};
