"use client"

import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils" // Assuming you have a utility for class names
import { useRunningProfile } from "@/app/contexts/RunningProfileContext" // To get steps and current step

export function CreateProfileStepper() {
  const router = useRouter()
  const { steps, currentStep, setCurrentStep } = useRunningProfile()

  const handleStepClick = (index: number) => {
    // Check if the clicked index is within the original `steps` array bounds
    if (index >= 0 && index < steps.length) {
      setCurrentStep(index)
      router.push(steps[index].href)
    }
  }

  // Determine the range of steps to display
  const maxVisibleSteps = 5; // Adjust as needed (e.g., current + 2 before + 2 after)
  let startIndex = Math.max(0, currentStep - 2);
  let endIndex = Math.min(steps.length - 1, currentStep + 2);

  // Adjust range if near the start or end to show `maxVisibleSteps` if possible
  if (endIndex - startIndex + 1 < maxVisibleSteps) {
    if (startIndex === 0) {
      endIndex = Math.min(steps.length - 1, startIndex + maxVisibleSteps - 1);
    } else if (endIndex === steps.length - 1) {
      startIndex = Math.max(0, endIndex - maxVisibleSteps + 1);
    }
  }

  const visibleSteps = steps.slice(startIndex, endIndex + 1);
  // We need the original index for navigation and display logic
  const stepData = visibleSteps.map((step, index) => ({ ...step, originalIndex: startIndex + index }));

  const showStartEllipsis = startIndex > 0;
  const showEndEllipsis = endIndex < steps.length - 1;

  return (
    <nav aria-label="Progress" className="w-full mt-6 mb-8">
      <ol role="list" className="flex items-center justify-center space-x-1 sm:space-x-2 bg-white dark:bg-terminal-black border border-brand-black/10 dark:border-text-main/10 rounded-full p-1.5 shadow-sm">
        {showStartEllipsis && (
          <li className="text-gray-500 dark:text-gray-400 px-1">...</li>
        )}

        {stepData.map((stepInfo) => {
          const index = stepInfo.originalIndex; // Use original index for logic
          const isCurrent = index === currentStep;
          const isCompleted = index < currentStep;

          return (
            <motion.li
              key={stepInfo.title}
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: index * 0.05, type: "spring", stiffness: 300, damping: 20 }}
              className="relative"
            >
              <motion.button
                onClick={() => handleStepClick(index)}
                className={cn(
                  "flex items-center justify-center rounded-full border-2 transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-green",
                  isCurrent
                    ? "w-14 h-14 sm:w-16 sm:h-16 bg-brand-green border-brand-green text-white shadow-md"
                    : "w-8 h-8 sm:w-9 sm:h-9 border-gray-300 dark:border-gray-600 bg-white dark:bg-sweetspot-black text-brand-black/70 dark:text-text-main/70 hover:border-gray-400 dark:hover:border-gray-500",
                  isCompleted && !isCurrent && "bg-emerald-50 dark:bg-emerald-900/50 border-emerald-300 dark:border-emerald-700",
                )}
                aria-current={isCurrent ? "step" : undefined}
                whileHover={{ scale: isCurrent ? 1.05 : 1.1 }}
                whileTap={{ scale: 0.95 }}
              >
                {isCurrent ? (
                  <span className="text-center text-[10px] sm:text-xs font-medium px-1 leading-tight">{stepInfo.title}</span>
                ) : (
                  <span className="text-[10px] sm:text-xs font-semibold">{index + 1}</span>
                )}
              </motion.button>
            </motion.li>
          )
        })}

        {showEndEllipsis && (
          <li className="text-gray-500 dark:text-gray-400 px-1">...</li>
        )}
      </ol>
    </nav>
  )
}
